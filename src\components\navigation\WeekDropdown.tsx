import React from "react";
import { AnimatedDropdown } from "@/components/ui/animated-dropdown";
import { WeekFrame } from "@/types/dashboard";

interface WeekDropdownProps {
  selectedWeek: WeekFrame | null;
  availableWeeks: WeekFrame[];
  onWeekSelect: (week: WeekFrame) => void;
  isActive: boolean;
}

export const WeekDropdown: React.FC<WeekDropdownProps> = ({
  selectedWeek,
  availableWeeks,
  onWeekSelect,
  isActive,
}) => {
  // Clean up week labels - remove duplicate "Week" text and dots
  const weekItems = availableWeeks.map((week) => {
    // Extract just the number from "Week 1", "Week 2", etc.
    const weekNumber = week.replace('Week ', '');
    return {
      value: week,
      label: `Week ${weekNumber}`, // Clean format: "Week 1", "Week 2", etc.
      hasData: true, // Assuming all available weeks have data
      isSelected: week === selectedWeek,
    };
  });

  const handleWeekSelect = (value: string) => {
    onWeekSelect(value as Week<PERSON>rame);
  };

  // Clean trigger text - show just "Week X" or "Weekly" if none selected
  const triggerText = selectedWeek
    ? `Week ${selectedWeek.replace('Week ', '')}`
    : "Weekly";

  return (
    <AnimatedDropdown
      trigger={triggerText}
      items={weekItems}
      onSelect={handleWeekSelect}
      selectedValue={selectedWeek || undefined}
      isActive={isActive}
      className="relative"
      dropdownClassName="min-w-[120px]"
    />
  );
};

export default WeekDropdown;
