
/* Enhanced Dropdown Animations - Smooth and Beautiful */

/* Base dropdown animations */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-5px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-5px) scale(0.98);
  }
}

/* Item stagger animation */
@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply to Radix Select components */
[data-radix-select-content] {
  animation: dropdownSlideIn 0.15s ease-out !important;
  transform-origin: top center !important;
}

[data-radix-select-content][data-state="closed"] {
  animation: dropdownSlideOut 0.1s ease-in !important;
}

/* Select items */
[data-radix-select-item] {
  animation: itemFadeIn 0.15s ease-out !important;
  transition: all 0.1s ease !important;
}

[data-radix-select-item]:nth-child(1) { animation-delay: 0ms !important; }
[data-radix-select-item]:nth-child(2) { animation-delay: 15ms !important; }
[data-radix-select-item]:nth-child(3) { animation-delay: 30ms !important; }
[data-radix-select-item]:nth-child(4) { animation-delay: 45ms !important; }
[data-radix-select-item]:nth-child(5) { animation-delay: 60ms !important; }
[data-radix-select-item]:nth-child(6) { animation-delay: 75ms !important; }
[data-radix-select-item]:nth-child(7) { animation-delay: 90ms !important; }
[data-radix-select-item]:nth-child(8) { animation-delay: 105ms !important; }

/* Hover effects for select items */
[data-radix-select-item]:hover {
  transform: translateX(1px) !important;
  box-shadow: 0 1px 4px rgba(59, 130, 246, 0.1) !important;
}

/* Popover content animations */
[data-radix-popover-content] {
  animation: dropdownSlideIn 0.15s ease-out !important;
  transform-origin: var(--radix-popover-content-transform-origin) !important;
}

[data-radix-popover-content][data-state="closed"] {
  animation: dropdownSlideOut 0.1s ease-in !important;
}

/* Enhanced focus styles */
[data-radix-select-item][data-highlighted] {
  background-color: rgb(239, 246, 255) !important;
  color: rgb(29, 78, 216) !important;
  transform: translateX(1px) !important;
}

/* Smooth trigger animations */
[data-radix-select-trigger] {
  transition: all 0.15s ease !important;
}

[data-radix-select-trigger]:hover {
  border-color: rgb(147, 197, 253) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

[data-radix-select-trigger][data-state="open"] {
  border-color: rgb(59, 130, 246) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Calendar specific animations */
.rdp {
  animation: dropdownSlideIn 0.15s ease-out !important;
}

.rdp-day_button:hover {
  transform: scale(1.02) !important;
  transition: transform 0.1s ease !important;
}

/* Alert Dialog animations */
[data-radix-dialog-content] {
  animation: dropdownSlideIn 0.2s ease-out !important;
}

/* Custom dropdown improvements */
.select-content-custom {
  background: white !important;
  border: 1px solid rgb(229, 231, 235) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  animation: dropdownSlideIn 0.15s ease-out !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

.select-item-custom {
  padding: 8px 12px !important;
  cursor: pointer !important;
  transition: all 0.1s ease !important;
  animation: itemFadeIn 0.15s ease-out !important;
}

.select-item-custom:hover {
  background-color: rgb(239, 246, 255) !important;
  color: rgb(29, 78, 216) !important;
  transform: translateX(1px) !important;
}

/* Scrollbar styling for dropdowns */
.select-content-custom::-webkit-scrollbar {
  width: 4px !important;
}

.select-content-custom::-webkit-scrollbar-track {
  background: rgb(243, 244, 246) !important;
  border-radius: 2px !important;
}

.select-content-custom::-webkit-scrollbar-thumb {
  background: rgb(209, 213, 219) !important;
  border-radius: 2px !important;
}

.select-content-custom::-webkit-scrollbar-thumb:hover {
  background: rgb(156, 163, 175) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  [data-radix-select-content] {
    max-height: 60vh !important;
  }
  
  [data-radix-select-item] {
    padding: 12px 16px !important;
    min-height: 44px !important;
  }
}

/* Remove any conflicting left slide animations */
[data-radix-select-content][data-side="left"],
[data-radix-select-content][data-side="right"] {
  animation: dropdownSlideIn 0.15s ease-out !important;
}

/* Ensure consistent z-index */
[data-radix-select-content],
[data-radix-popover-content],
[data-radix-dialog-content] {
  z-index: 9999 !important;
}

/* Enhanced Table Scrollbar Styling */
.table-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Top Scrollbar Styling */
.overflow-x-auto.overflow-y-hidden {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #e2e8f0;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar {
  height: 12px;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar-track {
  background: #e2e8f0;
  border-radius: 6px;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.overflow-x-auto.overflow-y-hidden::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Real-time Update Animations */
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.deleting-row {
  animation: fadeOut 0.3s ease-out forwards;
}

.updating-row {
  background-color: #fef3c7 !important;
  transition: background-color 0.3s ease;
}

.updated-row {
  animation: fadeIn 0.3s ease-out;
  background-color: #d1fae5 !important;
}
