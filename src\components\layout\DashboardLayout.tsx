
import React from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import Sidebar from "./Sidebar";
import { Search, Bell, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { WeekFrame } from "@/types/dashboard";
import { WeekDropdown } from "@/components/navigation/WeekDropdown";
import { MonthDropdown } from "@/components/navigation/MonthDropdown";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const {
    timeFrame,
    setTimeFrame,
    selectedMonth,
    setSelectedMonth,
    selectedDate,
    setSelectedDate,
    selectedWeek,
    setSelectedWeek,
    getAvailableMonths,
    getAvailableWeeks
  } = useDashboard();

  // Get available months and weeks dynamically
  const availableMonths = getAvailableMonths();
  const availableWeeks = getAvailableWeeks(selectedMonth);

  // Handle date picker change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      setTimeFrame("Daily");
    }
  };

  // Handle week selection
  const handleWeekSelect = (week: WeekFrame) => {
    setSelectedWeek(week);
    setTimeFrame("Weekly");
  };

  const handleMonthSelect = (month: string) => {
    setSelectedMonth(month);
    setSelectedWeek(null); // Reset week selection when month changes
    setTimeFrame("Monthly");
  };
  
  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      
      <div className="flex flex-col flex-1 overflow-hidden">
        <header className="h-16 border-b border-gray-200 flex items-center justify-between px-6 bg-white">
          <div className="flex items-center gap-4 w-full max-w-sm">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search..."
                className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-amplior-primary focus:border-transparent transition-all duration-200"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={timeFrame === "Daily" ? "default" : "outline"}
                size="sm"
                className="border-gray-200 transition-all duration-200 hover:scale-105"
                onClick={() => setTimeFrame("Daily")}
              >
                Daily
              </Button>

              {/* Date Picker for Daily view */}
              {timeFrame === "Daily" && (
                <DatePicker
                  date={selectedDate}
                  onDateChange={handleDateChange}
                  placeholder="Select date"
                  className="w-[180px]"
                />
              )}

              {/* Week Dropdown for Weekly view */}
              <WeekDropdown
                selectedWeek={selectedWeek}
                availableWeeks={availableWeeks}
                onWeekSelect={handleWeekSelect}
                isActive={timeFrame === "Weekly"}
              />

              {/* Month Dropdown for Monthly view */}
              <MonthDropdown
                selectedMonth={selectedMonth}
                availableMonths={availableMonths}
                onMonthSelect={handleMonthSelect}
                isActive={timeFrame === "Monthly"}
              />
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="transition-all duration-200 hover:scale-110">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="transition-all duration-200 hover:scale-110">
              <Settings className="h-5 w-5" />
            </Button>
            
            {/* FIXED: Updated user profile */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                PK
              </div>
              <div className="hidden md:block">
                <div className="text-sm font-semibold text-gray-900">Prashant Kanyal</div>
                <div className="text-xs text-gray-500">Data Analyst</div>
              </div>
            </div>
          </div>
        </header>
        
        <main className="flex-1 overflow-auto p-6 bg-amplior-background">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
