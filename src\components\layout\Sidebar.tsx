
import { useNavigate } from "react-router-dom";
import { useDashboard } from "@/contexts/DashboardContext";
import { <PERSON><PERSON>hart, BarChart3, Database, Users, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const Sidebar = () => {
  const navigate = useNavigate();
  const { 
    projects, 
    agents,
    selectedProject, 
    setSelectedProject, 
    currentView, 
    setCurrentView,
    setPerformanceView,
    setIsAgentPanelOpen,
    setIsNewProjectModalOpen,
    getAgentsByProjectId,
    getAgentInitials,
    getRandomColor,
    isLoading
  } = useDashboard();

  const handleProjectClick = (projectId: string) => {
    setSelectedProject(projectId);
    // When selecting a specific project, navigate to the performance view
    if (projectId !== "All") {
      setCurrentView('performance');
      setPerformanceView('agents');
      setIsAgentPanelOpen(true);
    }
  };

  const handleNavClick = (view: 'dashboard' | 'performance' | 'data-entry' | 'project-management') => {
    setCurrentView(view);
    // If navigating to performance view, reset to show all projects
    if (view === 'performance') {
      setSelectedProject("All");
      setPerformanceView("projects");
    }
  };

  const handleAddProject = () => {
    setIsNewProjectModalOpen(true);
  };

  // Get agents for the selected project
  const projectAgents = selectedProject !== "All" 
    ? getAgentsByProjectId(selectedProject) 
    : [];

  return (
    <div className="w-60 bg-white border-r border-gray-200 h-screen flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="bg-amplior-primary text-white rounded-md w-8 h-8 flex items-center justify-center">
            <span className="font-semibold">A</span>
          </div>
          <h1 className="font-semibold text-xl">Amplior</h1>
        </div>
      </div>

      <div className="overflow-y-auto flex-1">
        <div className="p-4">
          <div className="mb-6">
            <div 
              className={`sidebar-item ${currentView === 'dashboard' ? 'active' : ''}`}
              onClick={() => handleNavClick('dashboard')}
            >
              <PieChart size={18} />
              <span>Dashboard</span>
            </div>
            <div 
              className={`sidebar-item ${currentView === 'performance' ? 'active' : ''}`}
              onClick={() => handleNavClick('performance')}
            >
              <BarChart3 size={18} />
              <span>Project Performance</span>
            </div>
            <div 
              className={`sidebar-item ${currentView === 'project-management' ? 'active' : ''}`}
              onClick={() => handleNavClick('project-management')}
            >
              <Users size={18} />
              <span>Project Management</span>
            </div>
            {selectedProject !== "All" && (
              <div 
                className={`sidebar-item ${currentView === 'data-entry' ? 'active' : ''}`}
                onClick={() => handleNavClick('data-entry')}
              >
                <Database size={18} />
                <span>Data Entry</span>
              </div>
            )}
          </div>

          <div className="mb-2">
            <div className="flex justify-between items-center px-3 mb-2">
              <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                PROJECTS
              </h2>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-5 w-5 text-gray-500 hover:text-amplior-primary"
                onClick={handleAddProject}
              >
                <Plus size={14} />
              </Button>
            </div>
            
            <div
              className={cn(
                "sidebar-item",
                selectedProject === "All" ? "active" : ""
              )}
              onClick={() => handleProjectClick("All")}
            >
              <div className="avatar bg-gray-500">All</div>
              <span>All Projects</span>
            </div>
            
            {isLoading ? (
              <div className="flex justify-center py-4">
                <div className="animate-spin h-5 w-5 border-2 border-amplior-primary border-t-transparent rounded-full"></div>
              </div>
            ) : (
              projects.map((project) => (
                <div
                  key={project.id}
                  className={cn(
                    "sidebar-item",
                    selectedProject === project.id ? "active" : ""
                  )}
                  onClick={() => handleProjectClick(project.id)}
                >
                  <div className={cn("avatar", project.color)}>
                    {project.code}
                  </div>
                  <span>{project.name}</span>
                </div>
              ))
            )}
          </div>

          {selectedProject !== "All" && (
            <div className="mb-2 mt-4">
              <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 mb-2">
                PROJECT AGENTS
              </h2>
              {projectAgents.length === 0 ? (
                <div className="text-sm text-gray-500 px-3 py-2">
                  No agents assigned to this project.
                </div>
              ) : (
                projectAgents.map((agent) => (
                  <div key={agent.id} className="sidebar-item">
                    <div className={cn("avatar", getRandomColor())}>
                      {getAgentInitials(agent.name)}
                    </div>
                    <span>{agent.name}</span>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>

      <div className="p-3 border-t border-gray-200">
        <div className="flex items-center gap-3 p-2">
          <div className="avatar bg-red-500">RC</div>
          <div className="flex-1">
            <div className="text-sm font-medium">Rajesh Choudhari</div>
            <div className="text-xs text-gray-500">Sales Manager</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
