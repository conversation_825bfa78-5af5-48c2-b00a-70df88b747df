
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Check, Loader2, Sparkles, X, Search } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cleanAndFormatData, CleanedDataRow } from "@/services/dataCleaningService";
import { useDashboard } from "@/contexts/DashboardContext";
import { AgentPerformanceInsert } from "@/hooks/dashboard/useAgentPerformanceOperations";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";

interface DataCleaningModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: () => void;
}

interface DataPreview {
  entryCount: number;
  dateRange: string;
  agents: string[];
  projects: string[];
}

export const DataCleaningModal = ({
  open,
  onOpenChange,
  onImportComplete
}: DataCleaningModalProps) => {
  const { agents, projects, addPerformanceData, parseTalkTime } = useDashboard();
  const { toast } = useToast();
  
  // Enhanced state management
  const [selectedProject, setSelectedProject] = useState("");
  const [selectedAgent, setSelectedAgent] = useState("");
  const [agentSearch, setAgentSearch] = useState("");
  const [showAgentDropdown, setShowAgentDropdown] = useState(false);
  const [filteredAgents, setFilteredAgents] = useState(agents);
  const [rawData, setRawData] = useState("");
  const [cleanedData, setCleanedData] = useState<CleanedDataRow[]>([]);
  const [parsedPreview, setParsedPreview] = useState<DataPreview | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Filter agents based on project selection
  useEffect(() => {
    let filtered = agents;
    
    if (selectedProject) {
      filtered = agents.filter(agent => agent.projectId === selectedProject);
    }
    
    if (agentSearch) {
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(agentSearch.toLowerCase())
      );
    }
    
    setFilteredAgents(filtered);
  }, [selectedProject, agentSearch, agents]);

  const resetState = () => {
    setSelectedProject("");
    setSelectedAgent("");
    setAgentSearch("");
    setRawData("");
    setCleanedData([]);
    setParsedPreview(null);
    setIsProcessing(false);
    setIsImporting(false);
    setErrors([]);
    setShowAgentDropdown(false);
  };

  const handleProjectChange = (projectId: string) => {
    setSelectedProject(projectId);
    // Clear agent selection if not in the new project
    const projectAgents = agents.filter(agent => agent.projectId === projectId);
    if (selectedAgent && !projectAgents.find(a => a.id === selectedAgent)) {
      setSelectedAgent("");
      setAgentSearch("");
    }
  };

  const handleAgentSearch = (value: string) => {
    setAgentSearch(value);
    setShowAgentDropdown(true);
  };

  const selectAgent = (agent: any) => {
    setSelectedAgent(agent.id);
    setAgentSearch(agent.name);
    setShowAgentDropdown(false);
  };

  const getPlaceholderText = () => {
    const project = projects.find(p => p.id === selectedProject);
    const agent = agents.find(a => a.id === selectedAgent);
    
    if (project && agent) {
      return `Paste performance data for ${agent.name} from ${project.name} project.

Example format:
05/22/2025 May Week 4 ${project.name} ${agent.name} 55 23 0:20:25 12 8
${agent.name} 05/23/2025 ${project.name} Dials: 65 Connected: 28 Talk Time: 0:25:30 Scheduled: 10 Successful: 7`;
    }
    return 'Select project and agent first, then paste your raw performance data here...';
  };

  const generatePreview = (data: CleanedDataRow[]): DataPreview | null => {
    if (!data || data.length === 0) return null;

    const dates = data.map(entry => entry.date).filter(Boolean).sort();
    const agentNames = [...new Set(data.map(entry => entry.agent_name).filter(Boolean))];
    const projectNames = [...new Set(data.map(entry => entry.project_name).filter(Boolean))];

    return {
      entryCount: data.length,
      dateRange: dates.length > 0 ? `${dates[0]} to ${dates[dates.length - 1]}` : 'No valid dates',
      agents: agentNames,
      projects: projectNames
    };
  };

  const handleProcessData = async () => {
    if (!selectedProject) {
      toast({
        title: "Project Required",
        description: "Please select a project first",
        variant: "destructive",
      });
      return;
    }
    
    if (!selectedAgent) {
      toast({
        title: "Agent Required", 
        description: "Please select an agent first",
        variant: "destructive",
      });
      return;
    }
    
    if (!rawData.trim()) {
      toast({
        title: "Data Required",
        description: "Please paste the raw data first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setErrors([]);
    
    try {
      const project = projects.find(p => p.id === selectedProject);
      const agent = agents.find(a => a.id === selectedAgent);
      
      console.log("Processing with context:", {
        project: project?.name,
        agent: agent?.name,
        rawDataLength: rawData.length
      });

      // Enhanced raw data with context
      const contextualizedData = `
PROJECT: ${project?.name}
AGENT: ${agent?.name}
DATA:
${rawData}
      `.trim();

      const cleaned = await cleanAndFormatData(contextualizedData);
      console.log("Cleaned data received:", cleaned);
      
      // Filter results to match selected project and agent
      const filteredCleaned = cleaned.filter(row => {
        const agentMatch = !agent?.name || 
          row.agent_name.toLowerCase().includes(agent.name.toLowerCase());
        const projectMatch = !project?.name || 
          row.project_name.toLowerCase().includes(project.name.toLowerCase());
        return agentMatch && projectMatch;
      });

      setCleanedData(filteredCleaned);
      
      // Generate preview
      const preview = generatePreview(filteredCleaned);
      setParsedPreview(preview);
      
      // Validate against existing projects and agents
      const validationErrors: string[] = [];
      filteredCleaned.forEach((row, index) => {
        if (row.error_message) {
          validationErrors.push(`Row ${index + 1}: ${row.error_message}`);
        }
      });
      
      setErrors(validationErrors);
      
      if (validationErrors.length === 0 && filteredCleaned.length > 0) {
        toast({
          title: "Success",
          description: `Processed ${filteredCleaned.length} records successfully for ${agent?.name}`,
        });
      } else if (filteredCleaned.length === 0) {
        toast({
          title: "No Data Found",
          description: `No data found matching ${agent?.name} from ${project?.name}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error processing data:", error);
      setErrors([`Failed to process data: ${error.message}`]);
      toast({
        title: "Processing Failed",
        description: "Unable to clean the data. Please check the format and try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImportData = async () => {
    if (cleanedData.length === 0 || errors.length > 0) return;
    
    setIsImporting(true);
    
    try {
      let successCount = 0;
      let failureCount = 0;
      
      for (const row of cleanedData) {
        try {
          const performanceData: AgentPerformanceInsert = {
            agent_id: selectedAgent,
            project_id: selectedProject,
            date: row.date,
            dials: row.total_dials,
            connected: row.connected_calls,
            talk_time: parseTalkTime(row.talk_time),
            scheduled_meetings: row.scheduled_meetings,
            successful_meetings: row.successful_meetings,
            fop_scheduled: 0,
            fop_successful: 0,
          };
          
          const success = await addPerformanceData(performanceData);
          if (success) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (error) {
          console.error("Error importing row:", error);
          failureCount++;
        }
      }
      
      toast({
        title: "Import Complete",
        description: `Successfully imported ${successCount} records. ${failureCount} failed.`,
      });
      
      onImportComplete();
      onOpenChange(false);
      resetState();
    } catch (error) {
      console.error("Error during import:", error);
      toast({
        title: "Import Failed",
        description: "An error occurred during import. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            AI Data Cleaning & Import
          </DialogTitle>
          <DialogDescription>
            Select project and agent, then paste raw data. AI will filter and format it for import.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6">
          {/* Project Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Select Project</label>
            <Select value={selectedProject} onValueChange={handleProjectChange}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a project..." />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    <div className="flex items-center gap-2">
                      <span
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: project.color }}
                      ></span>
                      {project.name} ({project.code})
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Agent Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Select Agent</label>
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search or select agent..."
                  className="pl-10"
                  value={agentSearch}
                  onChange={(e) => handleAgentSearch(e.target.value)}
                  onFocus={() => setShowAgentDropdown(true)}
                  disabled={!selectedProject}
                />
              </div>
              
              {showAgentDropdown && filteredAgents.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                  {filteredAgents.map(agent => (
                    <div
                      key={agent.id}
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                      onClick={() => selectAgent(agent)}
                    >
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                        {agent.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-sm text-gray-500">
                          {projects.find(p => p.id === agent.projectId)?.name}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Raw Data Input */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Raw Performance Data</label>
            <Textarea
              placeholder={getPlaceholderText()}
              value={rawData}
              onChange={(e) => setRawData(e.target.value)}
              className="min-h-[200px] font-mono text-sm"
              disabled={isProcessing || isImporting || !selectedProject || !selectedAgent}
            />
            <div className="text-sm text-gray-500">
              Paste data from Google Sheets or Excel. The AI will automatically detect and format the data based on your selected project and agent.
            </div>
          </div>

          {/* Data Preview */}
          {parsedPreview && (
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="font-medium text-gray-700 mb-2">Data Preview</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Detected Entries:</strong> {parsedPreview.entryCount}</div>
                <div><strong>Date Range:</strong> {parsedPreview.dateRange}</div>
                <div><strong>Agents Found:</strong> {parsedPreview.agents.join(', ') || 'None'}</div>
                <div><strong>Projects Detected:</strong> {parsedPreview.projects.join(', ') || 'None'}</div>
              </div>
            </div>
          )}

          <Button
            onClick={handleProcessData}
            disabled={!rawData.trim() || !selectedProject || !selectedAgent || isProcessing || isImporting}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing with AI...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Clean & Format Data
              </>
            )}
          </Button>

          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Validation Errors</AlertTitle>
              <AlertDescription>
                <div className="max-h-32 overflow-y-auto text-xs space-y-1">
                  {errors.map((error, i) => (
                    <div key={i}>{error}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {cleanedData.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Cleaned Data Preview</h3>
                <span className="text-xs text-gray-500">{cleanedData.length} records</span>
              </div>
              <div className="border rounded-lg max-h-80 overflow-y-auto">
                <table className="w-full text-xs">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="p-2 text-left">Date</th>
                      <th className="p-2 text-left">Project</th>
                      <th className="p-2 text-left">Agent</th>
                      <th className="p-2 text-left">Dials</th>
                      <th className="p-2 text-left">Connected</th>
                      <th className="p-2 text-left">Talk Time</th>
                      <th className="p-2 text-left">Scheduled</th>
                      <th className="p-2 text-left">Successful</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cleanedData.map((row, index) => (
                      <tr key={index} className={row.error_message ? "bg-red-50" : "hover:bg-gray-50"}>
                        <td className="p-2">{row.date}</td>
                        <td className="p-2">{row.project_name}</td>
                        <td className="p-2">{row.agent_name}</td>
                        <td className="p-2">{row.total_dials}</td>
                        <td className="p-2">{row.connected_calls}</td>
                        <td className="p-2">{row.talk_time}</td>
                        <td className="p-2">{row.scheduled_meetings}</td>
                        <td className="p-2">{row.successful_meetings}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {cleanedData.length > 0 && errors.length === 0 && (
            <Alert className="bg-green-50 border-green-200">
              <Check className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Ready to Import</AlertTitle>
              <AlertDescription className="text-green-700">
                All data has been cleaned and validated. Ready for import to Supabase.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row sm:justify-between gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              onOpenChange(false);
              resetState();
            }}
            className="w-full sm:w-auto"
            disabled={isProcessing || isImporting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="w-full sm:w-auto bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            onClick={handleImportData}
            disabled={cleanedData.length === 0 || errors.length > 0 || isProcessing || isImporting}
          >
            {isImporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Importing...
              </>
            ) : (
              "Import to Database"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
