
import { useState, useEffect } from "react";
import { Agent } from "@/types/dashboard";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

export const useAgentOperations = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoadingAgents, setIsLoadingAgents] = useState(true);

  // Fetch agents from Supabase
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setIsLoadingAgents(true);
        const { data, error } = await supabase
          .from('agents')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;
        if (data) {
          // Map database field 'project_id' to our interface field 'projectId'
          const mappedAgents = data.map(agent => ({
            id: agent.id,
            name: agent.name,
            projectId: agent.project_id, // Map project_id to projectId
            created_at: agent.created_at,
            updated_at: agent.updated_at
          })) as Agent[];
          
          setAgents(mappedAgents);
        }
      } catch (error) {
        console.error('Error fetching agents:', error);
        toast({
          title: "Error",
          description: "Failed to load agents. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingAgents(false);
      }
    };

    fetchAgents();

    // Subscribe to realtime changes for agents
    const agentsSubscription = supabase
      .channel('agents-changes')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'agents' 
      }, (payload) => {
        if (payload.eventType === 'INSERT') {
          const newAgent = {
            id: payload.new.id,
            name: payload.new.name,
            projectId: payload.new.project_id, // Map project_id to projectId
            created_at: payload.new.created_at,
            updated_at: payload.new.updated_at
          } as Agent;
          
          setAgents((current) => [newAgent, ...current]);
        } else if (payload.eventType === 'UPDATE') {
          setAgents((current) => 
            current.map(agent => agent.id === payload.new.id ? {
              id: payload.new.id,
              name: payload.new.name,
              projectId: payload.new.project_id, // Map project_id to projectId
              created_at: payload.new.created_at,
              updated_at: payload.new.updated_at
            } as Agent : agent)
          );
        } else if (payload.eventType === 'DELETE') {
          setAgents((current) => 
            current.filter(agent => agent.id !== payload.old.id)
          );
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(agentsSubscription);
    };
  }, []);

  // Add a new agent to Supabase
  const addAgent = async (agent: Omit<Agent, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      // Map our interface field 'projectId' to database field 'project_id'
      const dbAgent = {
        name: agent.name,
        project_id: agent.projectId
      };

      const { error } = await supabase
        .from('agents')
        .insert([dbAgent]);

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Agent added successfully",
      });
      return true;
    } catch (error) {
      console.error('Error adding agent:', error);
      toast({
        title: "Error",
        description: "Failed to add agent. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  // Delete an agent from Supabase
  const deleteAgent = async (agentId: string) => {
    try {
      const { error } = await supabase
        .from('agents')
        .delete()
        .eq('id', agentId);

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Agent removed successfully",
      });
      return true;
    } catch (error) {
      console.error('Error removing agent:', error);
      toast({
        title: "Error",
        description: "Failed to remove agent. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  // Get agents for a specific project
  const getAgentsByProjectId = (projectId: string): Agent[] => {
    if (projectId === "All" || projectId === "all") {
      return agents;
    }
    return agents.filter(agent => agent.projectId === projectId);
  };

  return {
    agents,
    isLoadingAgents,
    addAgent,
    deleteAgent,
    getAgentsByProjectId,
  };
};
