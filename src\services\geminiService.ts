
const GEMINI_API_KEY = "AIzaSyBqPjcRRufvlA5nlAKHiMUzNoudB3V3VEs";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent";

export interface GeminiRequest {
  contents: {
    role: string;
    parts: {
      text: string;
    }[];
  }[];
}

export interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
  }[];
}

export const correctCSVWithAI = async (csvContent: string, errorMessage: string): Promise<string> => {
  try {
    const prompt = `
I have a CSV file with the following headers:
"Date,Hour,Minute,Second,Total Dials,Total Connected,Total Talk Time (mins),Scheduled Meetings,Successful Meetings,Agent Name"

The validation error is: "${errorMessage}"

Here's the CSV content:
${csvContent}

Please fix the error in the CSV content and return ONLY the corrected CSV content.
`;

    const request: GeminiRequest = {
      contents: [
        {
          role: "user",
          parts: [{ text: prompt }]
        }
      ]
    };

    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error("Failed to get response from Gemini API");
    }

    const data = await response.json() as GeminiResponse;
    const correctedCSV = data.candidates[0].content.parts[0].text;
    
    // Clean up the response - sometimes AI models add markdown code blocks
    return correctedCSV.replace(/```csv\n|```\n|```/g, '').trim();
  } catch (error) {
    console.error("Error using Gemini API:", error);
    throw error;
  }
};
