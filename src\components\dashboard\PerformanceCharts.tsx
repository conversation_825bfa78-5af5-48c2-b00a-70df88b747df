
import { useState, useEffect } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from "recharts";
import { ChartContainer } from "@/components/ui/chart";
import { AlertCircle, TrendingUp } from "lucide-react";

const PerformanceCharts = () => {
  const { getFilteredMetrics, timeFrame, setTimeFrame } = useDashboard();
  const [animationDelay, setAnimationDelay] = useState(0);

  const metrics = getFilteredMetrics();

  // Group metrics by week for the new Performance Comparison chart
  const chartData = metrics.reduce((acc: any[], metric) => {
    const weekKey = metric.week || `Week ${Math.ceil(new Date(metric.date).getDate() / 7)}`;
    const existingEntry = acc.find(entry => entry.week === weekKey);

    if (existingEntry) {
      existingEntry.scheduled += metric.scheduledMeetings;
      existingEntry.successful += metric.successfulMeetings;
    } else {
      acc.push({
        week: weekKey,
        scheduled: metric.scheduledMeetings,
        successful: metric.successfulMeetings,
      });
    }
    return acc;
  }, []).sort((a, b) => {
    const weekOrder = ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5'];
    return weekOrder.indexOf(a.week) - weekOrder.indexOf(b.week);
  });



  // Conversion funnel data
  const totalScheduled = chartData.reduce((sum, item) => sum + item.scheduled, 0);
  const totalSuccessful = chartData.reduce((sum, item) => sum + item.successful, 0);
  const totalDials = metrics.reduce((sum, metric) => sum + metric.dials, 0);
  const totalConnected = metrics.reduce((sum, metric) => sum + metric.connected, 0);
  
  // FIXED: Define totalQualified variable
  const totalQualified = totalConnected;

  const conversionData = [
    { name: 'Qualified Rate', value: totalDials > 0 ? ((totalConnected / totalDials) * 100) : 0, color: '#3498db' }, // Light Blue
    { name: 'Scheduled Rate', value: totalConnected > 0 ? ((totalScheduled / totalConnected) * 100) : 0, color: '#8e44ad' }, // Purple
    { name: 'Successful Rate', value: totalScheduled > 0 ? ((totalSuccessful / totalScheduled) * 100) : 0, color: '#2ecc71' }, // Green
  ];

  const chartConfig = {
    scheduled: {
      label: "Scheduled",
      color: "#8b5cf6", // Purple
    },
    successful: {
      label: "Successful",
      color: "#10b981", // Green
    },
  };

  // Animation effect for staggered loading
  useEffect(() => {
    setAnimationDelay(0);
    const timer = setTimeout(() => setAnimationDelay(150), 100);
    return () => clearTimeout(timer);
  }, [chartData]);

  // Enhanced loading state with skeleton placeholders
  if (chartData.length === 0) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
              <TrendingUp className="h-5 w-5" />
            </div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Performance Analytics
            </h2>
          </div>
          <div className="flex gap-2">
            {["Weekly", "Monthly", "Quarterly", "Yearly"].map((filter) => (
              <Button
                key={filter}
                variant={timeFrame === filter ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeFrame(filter as any)}
                className="text-xs transition-all duration-300 hover:scale-105 hover:shadow-md"
              >
                {filter}
              </Button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Performance Comparison Skeleton */}
          <Card className="animate-pulse transition-all duration-300 hover:shadow-lg">
            <CardHeader>
              <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-200 to-blue-200 rounded-full flex items-center justify-center mx-auto">
                    <AlertCircle className="h-6 w-6 text-purple-500" />
                  </div>
                  <p className="text-gray-500 font-medium">Loading performance data...</p>
                  <div className="flex space-x-1 justify-center">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                        style={{ animationDelay: `${i * 0.1}s` }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Conversion Funnel Skeleton */}
          <Card className="animate-pulse transition-all duration-300 hover:shadow-lg">
            <CardHeader>
              <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-2/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-200 to-green-200 rounded-full flex items-center justify-center mx-auto">
                    <AlertCircle className="h-6 w-6 text-blue-500" />
                  </div>
                  <p className="text-gray-500 font-medium">Loading conversion data...</p>
                  <div className="flex space-x-1 justify-center">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                        style={{ animationDelay: `${i * 0.1}s` }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
            <TrendingUp className="h-5 w-5" />
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Performance Analytics
          </h2>
        </div>
        <div className="flex gap-2">
          {["Weekly", "Monthly", "Quarterly", "Yearly"].map((filter) => (
            <Button
              key={filter}
              variant={timeFrame === filter ? "default" : "outline"}
              size="sm"
              onClick={() => setTimeFrame(filter as any)}
              className="text-xs transition-all duration-300 hover:scale-105 hover:shadow-md"
            >
              {filter}
            </Button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Comparison Chart - Enhanced with click interactions */}
        <Card className="transition-all duration-300 hover:shadow-xl hover:-translate-y-1 group overflow-hidden">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold text-gray-800 group-hover:text-purple-600 transition-colors duration-300">
              Performance Comparison
            </CardTitle>
            <CardDescription className="text-gray-600 text-sm">
              Weekly comparison bars with average line indicators
            </CardDescription>
            <div className="flex gap-4 mt-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                <span className="text-sm text-gray-600">Scheduled</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm text-gray-600">Successful</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="w-full overflow-hidden">
              <ChartContainer config={chartConfig} className="h-[320px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 20, left: 20, bottom: 20 }}
                    onClick={(data) => {
                      if (data && data.activePayload) {
                        const weekData = data.activePayload[0]?.payload;
                        if (weekData) {
                          alert(`${weekData.week} Details:\nScheduled: ${weekData.scheduled}\nSuccessful: ${weekData.successful}\nSuccess Rate: ${((weekData.successful / weekData.scheduled) * 100).toFixed(1)}%`);
                        }
                      }
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="week"
                      tick={{ fontSize: 11, fill: '#666' }}
                      axisLine={{ stroke: '#e0e0e0' }}
                    />
                    <YAxis
                      tick={{ fontSize: 11, fill: '#666' }}
                      axisLine={{ stroke: '#e0e0e0' }}
                    />
                    <Tooltip
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const scheduled = payload.find(p => p.dataKey === 'scheduled')?.value || 0;
                          const successful = payload.find(p => p.dataKey === 'successful')?.value || 0;
                          const successRate = Number(scheduled) > 0 ? ((Number(successful) / Number(scheduled)) * 100).toFixed(1) : '0';

                          return (
                            <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                              <p className="font-semibold text-gray-800 mb-2">{label}</p>
                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                                  <span className="text-sm">Scheduled: <strong>{scheduled}</strong></span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                  <span className="text-sm">Successful: <strong>{successful}</strong></span>
                                </div>
                                <div className="pt-1 border-t border-gray-100">
                                  <span className="text-sm font-semibold text-blue-600">Success Rate: {successRate}%</span>
                                </div>
                              </div>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Legend />

                    {/* Main bars with enhanced styling and click interactions */}
                    <Bar
                      dataKey="scheduled"
                      fill="#8b5cf6"
                      name="Scheduled"
                      radius={[4, 4, 0, 0]}
                      className="transition-all duration-300 hover:opacity-80 cursor-pointer"
                      animationBegin={animationDelay}
                      animationDuration={800}
                    />
                    <Bar
                      dataKey="successful"
                      fill="#10b981"
                      name="Successful"
                      radius={[4, 4, 0, 0]}
                      className="transition-all duration-300 hover:opacity-80 cursor-pointer"
                      animationBegin={animationDelay + 150}
                      animationDuration={800}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
            <div className="mt-3 text-center text-xs text-gray-500">
              Click on bars for detailed metrics • Only showing available data
            </div>
          </CardContent>
        </Card>

        {/* Conversion Funnel Chart - Enhanced with click interactions */}
        <Card className="transition-all duration-300 hover:shadow-xl hover:-translate-y-1 group overflow-hidden">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">
              Conversion Funnel
            </CardTitle>
            <CardDescription className="text-gray-600 text-sm">
              Performance conversion rates at each stage
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4">
            <div className="h-[320px] flex items-center justify-center">
              <div className="relative">
                {/* Interactive Donut Chart */}
                <ResponsiveContainer width={240} height={240}>
                  <PieChart>
                    <Pie
                      data={conversionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={3}
                      dataKey="value"
                      animationBegin={animationDelay + 300}
                      animationDuration={1000}
                      onClick={(data: any) => {
                        if (data) {
                          const actualValue = data.name === 'Qualified Rate' ? totalQualified :
                                            data.name === 'Scheduled Rate' ? totalScheduled :
                                            totalSuccessful;
                          alert(`${data.name} Details:\nPercentage: ${data.value.toFixed(1)}%\nActual Count: ${actualValue}\nCalculation: Based on total pipeline metrics`);
                        }
                      }}
                    >
                      {conversionData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.color}
                          className="transition-all duration-300 hover:opacity-80 cursor-pointer"
                          stroke="#fff"
                          strokeWidth={2}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      content={({ active, payload }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0].payload;
                          const actualValue = data.name === 'Qualified Rate' ? totalQualified :
                                            data.name === 'Scheduled Rate' ? totalScheduled :
                                            totalSuccessful;

                          return (
                            <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                              <p className="font-semibold text-gray-800 mb-2">{data.name}</p>
                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: data.color }}></div>
                                  <span className="text-sm">Rate: <strong>{data.value.toFixed(1)}%</strong></span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  Count: <strong>{actualValue}</strong>
                                </div>
                              </div>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>

                {/* Center Statistics */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-xl font-bold text-gray-800">
                      {totalScheduled > 0 ? ((totalSuccessful / totalScheduled) * 100).toFixed(1) : 0}%
                    </div>
                    <div className="text-xs text-gray-500">Overall Success</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Conversion Metrics */}
            <div className="grid grid-cols-3 gap-3 mt-4">
              {conversionData.map((item, index) => (
                <div
                  key={index}
                  className="text-center p-3 bg-gray-50 rounded-lg transition-all duration-200 hover:bg-gray-100 hover:scale-105 cursor-pointer"
                  onClick={() => {
                    const actualValue = item.name === 'Qualified Rate' ? totalQualified :
                                      item.name === 'Scheduled Rate' ? totalScheduled :
                                      totalSuccessful;
                    alert(`${item.name} Details:\nPercentage: ${item.value.toFixed(1)}%\nActual Count: ${actualValue}`);
                  }}
                >
                  <div className="text-base font-bold" style={{ color: item.color }}>
                    {item.value.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    {item.name}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-3 text-center text-xs text-gray-500">
              Click on chart or metrics for detailed breakdown
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PerformanceCharts;
