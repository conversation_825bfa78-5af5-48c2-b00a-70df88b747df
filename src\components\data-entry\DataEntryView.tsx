
import React, { useState } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { format } from "date-fns";
import { Calendar, Save, RotateCcw, TrendingUp, FileDown, Upload, Sparkles, X, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { AgentPerformanceInsert } from "@/hooks/dashboard/useAgentPerformanceOperations";
import { cn } from "@/lib/utils";
import { downloadCSV, generateSampleCSV } from "@/utils/csvUtils";
import { BulkImportModal } from "./BulkImportModal";
import { DataCleaningModal } from "./DataCleaningModal";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const dataEntrySchema = z.object({
  agentId: z.string({
    required_error: "Please select an agent",
  }),
  date: z.date({
    required_error: "Please select a date",
  }),
  week: z.string().optional(),
  dials: z.coerce
    .number()
    .min(0, "Must be at least 0")
    .max(10000, "Value too high"),
  connected: z.coerce
    .number()
    .min(0, "Must be at least 0")
    .max(10000, "Value too high"),
  talkTime: z.string()
    .regex(/^\d{2}:\d{2}:\d{2}$/, "Must be in HH:MM:SS format"),
  scheduledMeetings: z.coerce
    .number()
    .min(0, "Must be at least 0")
    .max(1000, "Value too high"),
  successfulMeetings: z.coerce
    .number()
    .min(0, "Must be at least 0")
    .max(1000, "Value too high"),
  fopScheduled: z.coerce
    .number()
    .min(0, "Must be at least 0")
    .max(1000, "Value too high"),
  fopSuccessful: z.coerce
    .number()
    .min(0, "Must be at least 0")
    .max(1000, "Value too high"),
  fopProjects: z.array(z.string()).optional(),
});

type DataEntryFormValues = z.infer<typeof dataEntrySchema>;

// Enhanced Time Input Component
interface EnhancedTimeInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const EnhancedTimeInput: React.FC<EnhancedTimeInputProps> = ({
  value,
  onChange,
  placeholder = "00:00:00",
  className,
  disabled = false,
}) => {
  const [hours, setHours] = useState(value.split(':')[0] || '00');
  const [minutes, setMinutes] = useState(value.split(':')[1] || '00');
  const [seconds, setSeconds] = useState(value.split(':')[2] || '00');

  React.useEffect(() => {
    const parts = value.split(':');
    setHours(parts[0] || '00');
    setMinutes(parts[1] || '00');
    setSeconds(parts[2] || '00');
  }, [value]);

  const updateTime = (newHours: string, newMinutes: string, newSeconds: string) => {
    const formattedTime = `${newHours.padStart(2, '0')}:${newMinutes.padStart(2, '0')}:${newSeconds.padStart(2, '0')}`;
    onChange(formattedTime);
  };

  const handleHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHours = Math.min(23, Math.max(0, parseInt(e.target.value) || 0)).toString();
    setHours(newHours);
    updateTime(newHours, minutes, seconds);
  };

  const handleMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMinutes = Math.min(59, Math.max(0, parseInt(e.target.value) || 0)).toString();
    setMinutes(newMinutes);
    updateTime(hours, newMinutes, seconds);
  };

  const handleSecondsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSeconds = Math.min(59, Math.max(0, parseInt(e.target.value) || 0)).toString();
    setSeconds(newSeconds);
    updateTime(hours, minutes, newSeconds);
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <Input
          type="number"
          min="0"
          max="23"
          value={hours}
          onChange={handleHoursChange}
          className="w-16 text-center font-mono"
          disabled={disabled}
          placeholder="00"
        />
        <span className="text-gray-500 font-mono">:</span>
        <Input
          type="number"
          min="0"
          max="59"
          value={minutes}
          onChange={handleMinutesChange}
          className="w-16 text-center font-mono"
          disabled={disabled}
          placeholder="00"
        />
        <span className="text-gray-500 font-mono">:</span>
        <Input
          type="number"
          min="0"
          max="59"
          value={seconds}
          onChange={handleSecondsChange}
          className="w-16 text-center font-mono"
          disabled={disabled}
          placeholder="00"
        />
      </div>
    </div>
  );
};

const DataEntryView = () => {
  const { selectedProject, getProjectAgents, getProjectById, addPerformanceData, parseTalkTime, projects } = useDashboard();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isBulkImportModalOpen, setIsBulkImportModalOpen] = useState(false);
  const [isDataCleaningModalOpen, setIsDataCleaningModalOpen] = useState(false);
  const [selectedFopProjects, setSelectedFopProjects] = useState<string[]>([]);
  
  const project = getProjectById(selectedProject);
  const agents = getProjectAgents(selectedProject);

  // Generate week options (Week 1-5 only)
  const weekOptions = Array.from({ length: 5 }, (_, i) => ({
    value: `Week ${i + 1}`,
    label: `Week ${i + 1}`
  }));

  const form = useForm<DataEntryFormValues>({
    resolver: zodResolver(dataEntrySchema),
    defaultValues: {
      agentId: "",
      date: new Date(),
      week: "",
      dials: 0,
      connected: 0,
      talkTime: "00:00:00",
      scheduledMeetings: 0,
      successfulMeetings: 0,
      fopScheduled: 0,
      fopSuccessful: 0,
      fopProjects: [],
    },
  });

  const onSubmit = async (values: DataEntryFormValues) => {
    setIsSubmitting(true);
    
    try {
      const selectedAgent = agents.find(a => a.id === values.agentId);
      if (!selectedAgent) {
        throw new Error("Selected agent not found");
      }

      const performanceData: AgentPerformanceInsert = {
        agent_id: values.agentId,
        project_id: selectedAgent.projectId,
        date: format(values.date, "yyyy-MM-dd"),
        week: values.week,
        dials: values.dials,
        connected: values.connected,
        talk_time: parseTalkTime(values.talkTime),
        scheduled_meetings: values.scheduledMeetings,
        successful_meetings: values.successfulMeetings,
        fop_scheduled: values.fopScheduled,
        fop_successful: values.fopSuccessful,
        fop_projects: selectedFopProjects, // FIXED: Include FOP projects in submission
      };

      console.log('Submitting performance data:', performanceData);

      const success = await addPerformanceData(performanceData);
      
      if (success) {
        // Reset form with empty values
        form.reset({
          agentId: "",
          date: new Date(),
          week: "",
          dials: 0,
          connected: 0,
          talkTime: "00:00:00",
          scheduledMeetings: 0,
          successfulMeetings: 0,
          fopScheduled: 0,
          fopSuccessful: 0,
          fopProjects: [],
        });
        setSelectedFopProjects([]);
        
        toast({
          title: "Performance data saved successfully",
          description: "Data has been updated in real-time.",
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: "Error saving data",
        description: "An error occurred while saving. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    form.reset({
      agentId: "",
      date: new Date(),
      week: "",
      dials: 0,
      connected: 0,
      talkTime: "00:00:00",
      scheduledMeetings: 0,
      successfulMeetings: 0,
      fopScheduled: 0,
      fopSuccessful: 0,
      fopProjects: [],
    });
    setSelectedFopProjects([]);
  };

  const handleDownloadSampleCSV = () => {
    const filename = "sample_performance_data.csv";
    const content = generateSampleCSV();
    downloadCSV(filename, content);
    
    toast({
      title: "Sample CSV Downloaded",
      description: "You can now fill it with your data and upload using Bulk Import.",
    });
  };

  const handleBulkImportComplete = () => {
    toast({
      title: "Bulk Import Completed",
      description: "Performance data has been successfully imported.",
    });
  };

  const handleDataCleaningComplete = () => {
    toast({
      title: "AI Data Import Completed",
      description: "Performance data has been cleaned and imported successfully.",
    });
  };

  const addFopProject = (projectId: string) => {
    if (!selectedFopProjects.includes(projectId)) {
      setSelectedFopProjects([...selectedFopProjects, projectId]);
    }
  };

  const removeFopProject = (projectId: string) => {
    setSelectedFopProjects(selectedFopProjects.filter(id => id !== projectId));
  };

  const availableProjects = projects.filter(
    p => p.id !== selectedProject && !selectedFopProjects.includes(p.id)
  );

  return (
    <div className="space-y-8">
      <div className="text-center lg:text-left">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl text-white">
            <TrendingUp className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Data Entry - {project?.name}
            </h1>
            <p className="text-gray-600 mt-1">
              Record daily performance data for your team members with real-time sync.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto">
        {/* Import buttons */}
        <div className="flex flex-col sm:flex-row gap-3 mb-4 justify-end">
          <Button
            variant="outline"
            className="h-10 flex items-center gap-2 border-blue-300 text-blue-700 hover:bg-blue-50"
            onClick={handleDownloadSampleCSV}
          >
            <FileDown className="w-4 h-4" />
            Download Sample CSV
          </Button>
          
          <Button
            className="h-10 flex items-center gap-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
            onClick={() => setIsDataCleaningModalOpen(true)}
          >
            <Sparkles className="w-4 h-4" />
            AI Data Cleaning
          </Button>
          
          <Button
            className="h-10 flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            onClick={() => setIsBulkImportModalOpen(true)}
          >
            <Upload className="w-4 h-4" />
            Bulk Import CSV
          </Button>
        </div>

        <div className="bg-white rounded-2xl border border-gray-100 shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Agent Performance Form
            </h2>
            <p className="text-sm text-gray-600 mt-1">All changes are synced in real-time</p>
          </div>

          {/* Form */}
          <div className="p-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* Basic Info Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                    Basic Information
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="agentId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Agent</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <FormControl>
                              <SelectTrigger className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                                <SelectValue placeholder="Select an agent" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="bg-white border border-gray-200 shadow-xl z-50">
                              {agents.map(agent => (
                                <SelectItem 
                                  key={agent.id} 
                                  value={agent.id}
                                  className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer"
                                >
                                  {agent.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Performance Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full h-11 justify-start text-left font-normal border-gray-300 hover:border-gray-400",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                                  {field.value ? format(field.value, "PPP") : "Select date"}
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0 bg-white border border-gray-200 shadow-xl z-50" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date > new Date() || date < new Date("2020-01-01")}
                                initialFocus
                                className="p-3 pointer-events-auto"
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription className="text-xs text-gray-500">
                            Date when the performance was recorded
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="week"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Week</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <FormControl>
                              <SelectTrigger className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                                <SelectValue placeholder="Select week" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="bg-white border border-gray-200 shadow-xl z-50">
                              {weekOptions.map(week => (
                                <SelectItem 
                                  key={week.value} 
                                  value={week.value}
                                  className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer"
                                >
                                  {week.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription className="text-xs text-gray-500">
                            Week for which performance is recorded (resets monthly)
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Performance Metrics Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                    Performance Metrics
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="dials"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Total Dials</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field} 
                              className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                              placeholder="0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Number of calls made
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="connected"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Connected Calls</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field} 
                              className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                              placeholder="0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Calls that connected
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="talkTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Talk Time</FormLabel>
                          <FormControl>
                            <EnhancedTimeInput
                              value={field.value}
                              onChange={field.onChange}
                              className="h-11"
                              placeholder="00:00:00"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Total talk time
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Primary Project Meeting Metrics Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                    Primary Project Meetings
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="scheduledMeetings"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Scheduled Meetings</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field} 
                              className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                              placeholder="0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Meetings scheduled for primary project
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="successfulMeetings"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Successful Meetings</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field} 
                              className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                              placeholder="0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Successful meetings for primary project
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* FOP (For Other Projects) Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                    FOP (For Other Projects)
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="fopScheduled"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Meetings Scheduled</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field} 
                              className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                              placeholder="0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Meetings scheduled for other projects
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="fopSuccessful"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">Meetings Successful</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0" 
                              {...field} 
                              className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                              placeholder="0"
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Successful meetings for other projects
                          </FormDescription>
                          <FormMessage className="text-red-500 text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Project Tags for FOP */}
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 block mb-2">
                        Projects (For Other Projects)
                      </label>
                      <div className="space-y-3">
                        {/* Selected Projects Tags */}
                        {selectedFopProjects.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {selectedFopProjects.map(projectId => {
                              const proj = projects.find(p => p.id === projectId);
                              return proj ? (
                                <Badge
                                  key={projectId}
                                  variant="secondary"
                                  className="bg-green-100 text-green-800 hover:bg-green-200 flex items-center gap-1 px-3 py-1"
                                >
                                  {proj.name}
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="h-auto p-0 ml-1 hover:bg-transparent"
                                    onClick={() => removeFopProject(projectId)}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        )}

                        {/* Project Selector */}
                        <Select onValueChange={addFopProject}>
                          <SelectTrigger className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                            <SelectValue placeholder="Select projects for FOP meetings..." />
                          </SelectTrigger>
                          <SelectContent className="bg-white border border-gray-200 shadow-xl z-50">
                            {availableProjects.map(proj => (
                              <SelectItem 
                                key={proj.id} 
                                value={proj.id}
                                className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer"
                              >
                                {proj.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Select the projects for which meetings were scheduled/completed
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row justify-end gap-4 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    className="h-11 px-6 border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                    disabled={isSubmitting}
                  >
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Reset Form
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="h-11 px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Performance Data
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={isBulkImportModalOpen}
        onOpenChange={setIsBulkImportModalOpen}
        onImportComplete={handleBulkImportComplete}
      />

      {/* AI Data Cleaning Modal */}
      <DataCleaningModal
        open={isDataCleaningModalOpen}
        onOpenChange={setIsDataCleaningModalOpen}
        onImportComplete={handleDataCleaningComplete}
      />
    </div>
  );
};

export default DataEntryView;
