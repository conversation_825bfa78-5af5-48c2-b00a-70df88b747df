/* Enhanced Animated Dropdown Styles */

/* Smooth slide-down animation for dropdown items */
@keyframes slideDownStagger {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dropdown container animations - smooth slide down */
@keyframes dropdownSlideDown {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes dropdownSlideUp {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
}

/* Apply smooth slide-down animation to dropdown items */
.dropdown-item {
  animation: slideDownStagger 0.3s ease-out forwards;
}

.dropdown-item:nth-child(1) { animation-delay: 0ms; }
.dropdown-item:nth-child(2) { animation-delay: 50ms; }
.dropdown-item:nth-child(3) { animation-delay: 100ms; }
.dropdown-item:nth-child(4) { animation-delay: 150ms; }
.dropdown-item:nth-child(5) { animation-delay: 200ms; }
.dropdown-item:nth-child(6) { animation-delay: 250ms; }
.dropdown-item:nth-child(7) { animation-delay: 300ms; }
.dropdown-item:nth-child(8) { animation-delay: 350ms; }
.dropdown-item:nth-child(9) { animation-delay: 400ms; }
.dropdown-item:nth-child(10) { animation-delay: 450ms; }
.dropdown-item:nth-child(11) { animation-delay: 500ms; }
.dropdown-item:nth-child(12) { animation-delay: 550ms; }

/* Enhanced hover effects */
.dropdown-item-with-data {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item-with-data:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
}

.dropdown-item-no-data:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Smooth chevron rotation */
.dropdown-chevron {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-chevron.rotated {
  transform: rotate(180deg);
}

/* Focus styles for accessibility */
.dropdown-item:focus {
  outline: 2px solid #2196F3;
  outline-offset: -2px;
}

/* Data indicator pulse animation */
@keyframes dataPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.data-indicator {
  animation: dataPulse 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dropdown-item {
    padding: 12px 16px;
    min-height: 44px; /* Touch-friendly minimum */
  }
}
