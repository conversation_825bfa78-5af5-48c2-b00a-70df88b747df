
import { useDashboard } from "@/contexts/DashboardContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import DashboardView from "@/components/dashboard/DashboardView";
import PerformanceView from "@/components/performance/PerformanceView";
import DataEntryTabs from "@/components/data-entry/DataEntryTabs";
import ProjectManagementView from "@/components/project-management/ProjectManagementView";

const Index = () => {
  const { currentView } = useDashboard();

  const renderView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardView />;
      case 'performance':
        return <PerformanceView />;
      case 'data-entry':
        return <DataEntryTabs />;
      case 'project-management':
        return <ProjectManagementView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <DashboardLayout>
      {renderView()}
    </DashboardLayout>
  );
};

export default Index;
