import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import "@/styles/dropdown-animations.css";

interface DropdownItem {
  value: string;
  label: string;
  hasData?: boolean;
  isSelected?: boolean;
}

interface AnimatedDropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  onSelect: (value: string) => void;
  selectedValue?: string;
  className?: string;
  dropdownClassName?: string;
  placeholder?: string;
  isActive?: boolean;
  unstyled?: boolean; // For form usage without button styling
}

export const AnimatedDropdown: React.FC<AnimatedDropdownProps> = ({
  trigger,
  items,
  onSelect,
  selectedValue,
  className,
  dropdownClassName,
  placeholder = "Select option",
  isActive = false,
  unstyled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [animationState, setAnimationState] = useState<'closed' | 'opening' | 'open' | 'closing'>('closed');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        closeDropdown();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          closeDropdown();
          break;
        case 'ArrowDown':
          event.preventDefault();
          // Focus next item logic could be added here
          break;
        case 'ArrowUp':
          event.preventDefault();
          // Focus previous item logic could be added here
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  const openDropdown = () => {
    setIsOpen(true);
    setAnimationState('opening');
    setTimeout(() => setAnimationState('open'), 50);
  };

  const closeDropdown = () => {
    setAnimationState('closing');
    setTimeout(() => {
      setIsOpen(false);
      setAnimationState('closed');
    }, 300);
  };

  const toggleDropdown = () => {
    if (isOpen) {
      closeDropdown();
    } else {
      openDropdown();
    }
  };

  const handleItemSelect = (value: string) => {
    onSelect(value);
    closeDropdown();
  };

  const getItemClassName = (item: DropdownItem, index: number) => {
    const baseClasses = "dropdown-item w-full px-4 py-3 text-left text-sm focus:outline-none border-l-2";

    let classes = baseClasses;

    // Data availability styling
    if (item.hasData) {
      classes += " dropdown-item-with-data bg-blue-50 border-l-blue-400 text-blue-900 hover:bg-blue-100";
    } else {
      classes += " dropdown-item-no-data bg-gray-50 border-l-gray-200 text-gray-500 hover:bg-gray-100";
    }

    // Selected state
    if (item.value === selectedValue) {
      classes += " font-semibold";
      if (item.hasData) {
        classes += " bg-blue-100 border-l-blue-600";
      } else {
        classes += " bg-gray-100 border-l-gray-400";
      }
    }

    return classes;
  };

  return (
    <div className={cn("relative", className)}>
      {unstyled ? (
        <div
          ref={triggerRef as any}
          className={cn(
            "w-full h-full cursor-pointer flex items-center",
            isOpen && "ring-2 ring-blue-200 rounded-md"
          )}
          onClick={toggleDropdown}
          role="button"
          aria-expanded={isOpen}
          aria-haspopup="true"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              toggleDropdown();
            }
          }}
        >
          {trigger}
        </div>
      ) : (
        <Button
          ref={triggerRef}
          variant={isActive ? "default" : "outline"}
          size="sm"
          className={cn(
            "border-gray-200 transition-all duration-200 hover:scale-105 flex items-center gap-2",
            isOpen && "ring-2 ring-blue-200"
          )}
          onClick={toggleDropdown}
          aria-expanded={isOpen}
          aria-haspopup="true"
        >
          {trigger}
          <ChevronDown
            className={cn(
              "h-4 w-4 dropdown-chevron",
              isOpen && "rotated"
            )}
          />
        </Button>
      )}

      {isOpen && (
        <div
          ref={dropdownRef}
          className={cn(
            "absolute top-full left-0 mt-2 min-w-[200px] bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden",
            "transition-all duration-300 ease-out",
            animationState === 'opening' && "opacity-0 transform scale-95 translate-y-[-10px]",
            animationState === 'open' && "opacity-100 transform scale-100 translate-y-0",
            animationState === 'closing' && "opacity-0 transform scale-95 translate-y-[-10px]",
            dropdownClassName
          )}
          style={{
            transformOrigin: 'top center',
            maxHeight: '300px',
            overflowY: 'auto',
          }}
        >
          <div className="py-2">
            {items.map((item, index) => (
              <button
                key={item.value}
                className={getItemClassName(item, index)}
                onClick={() => handleItemSelect(item.value)}
                style={{
                  animationDelay: `${index * 50}ms`,
                }}
              >
                <div className="flex items-center justify-between">
                  <span>{item.label}</span>
                  {item.hasData && (
                    <div className="w-2 h-2 bg-blue-400 rounded-full data-indicator"></div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnimatedDropdown;
