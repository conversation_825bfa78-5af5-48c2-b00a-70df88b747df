
import { format } from "date-fns";
import { AgentPerformanceInsert } from "@/hooks/dashboard/useAgentPerformanceOperations";
import papa from "papaparse";

// Sample header for CSV template
export const CSV_HEADER = "Date,Hour,Minute,Second,Total Dials,Total Connected,Total Talk Time (mins),Scheduled Meetings,Successful Meetings,Agent Name";

// Generate sample CSV content
export const generateSampleCSV = (): string => {
  const today = new Date();
  const formattedDate = format(today, "yyyy-MM-dd");
  
  // Create CSV content with header and sample row
  const content = [
    CSV_HEADER,
    `${formattedDate},10,15,30,25,18,120,5,3,<PERSON>`
  ].join('\n');
  
  return content;
};

// Helper function to download a CSV file
export const downloadCSV = (filename: string, content: string): void => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  // Create a URL for the blob and set it as the href for the link
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  
  // Append the link to the document, click it, and then remove it
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Helper to convert time values to seconds
export const convertToSeconds = (hours: number, minutes: number, seconds: number): number => {
  return (hours * 60 * 60) + (minutes * 60) + seconds;
};

// Interface for CSV row data
export interface CSVRowData {
  Date: string;
  Hour: string;
  Minute: string;
  Second: string;
  "Total Dials": string;
  "Total Connected": string;
  "Total Talk Time (mins)": string;
  "Scheduled Meetings": string;
  "Successful Meetings": string;
  "Agent Name": string;
}

// Parse CSV data
export const parseCSV = (csvContent: string): Promise<CSVRowData[]> => {
  return new Promise((resolve, reject) => {
    papa.parse(csvContent, {
      header: true,
      complete: (results) => {
        resolve(results.data as CSVRowData[]);
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

// Basic validation for CSV data
export const validateCSVRow = (row: CSVRowData, rowIndex: number): string | null => {
  // Check date format
  if (!/^\d{4}-\d{2}-\d{2}$/.test(row.Date)) {
    return `Error in Row ${rowIndex + 1}: 'Date' must be in YYYY-MM-DD format.`;
  }

  // Check numeric fields
  const numericFields = [
    { field: "Hour", max: 23 },
    { field: "Minute", max: 59 },
    { field: "Second", max: 59 },
    { field: "Total Dials", min: 0 },
    { field: "Total Connected", min: 0 },
    { field: "Total Talk Time (mins)", min: 0 },
    { field: "Scheduled Meetings", min: 0 },
    { field: "Successful Meetings", min: 0 }
  ];

  for (const { field, min, max } of numericFields) {
    const value = parseInt(row[field as keyof CSVRowData], 10);
    if (isNaN(value)) {
      return `Error in Row ${rowIndex + 1}: '${field}' must be a number.`;
    }
    if (min !== undefined && value < min) {
      return `Error in Row ${rowIndex + 1}: '${field}' must be greater than or equal to ${min}.`;
    }
    if (max !== undefined && value > max) {
      return `Error in Row ${rowIndex + 1}: '${field}' must be less than or equal to ${max}.`;
    }
  }

  // Check agent name is not empty
  if (!row["Agent Name"] || row["Agent Name"].trim() === "") {
    return `Error in Row ${rowIndex + 1}: 'Agent Name' cannot be empty.`;
  }

  return null; // No errors
};
