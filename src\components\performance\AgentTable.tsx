
import React from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, TrendingUp, Clock, Phone, Calendar, CheckCircle, Users } from "lucide-react";

interface AgentTableProps {
  onAgentSelect?: (agentId: string) => void;
}

const AgentTable: React.FC<AgentTableProps> = ({ onAgentSelect }) => {
  const { 
    selectedProject, 
    getAgentsByProjectId, 
    getAgentPerformanceMetrics, 
    formatTalkTime,
    getAgentInitials 
  } = useDashboard();

  const agents = getAgentsByProjectId(selectedProject);

  const getAgentMetrics = (agentId: string) => {
    return getAgentPerformanceMetrics(agentId);
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 70) return "text-green-600 bg-green-100";
    if (rate >= 50) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  if (agents.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <TrendingUp className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No agents found</h3>
          <p className="text-gray-500">There are no agents assigned to this project yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {agents.map((agent) => {
        const metrics = getAgentMetrics(agent.id);
        const initials = getAgentInitials(agent.name);
        
        return (
          <Card key={agent.id} className="transition-all duration-200 hover:shadow-lg border border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                    {initials}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900">{agent.name}</h3>
                    <p className="text-gray-500 text-sm">Agent ID: {agent.id.slice(-8).toUpperCase()}</p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-5 gap-4 text-center">
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Phone className="w-3 h-3" />
                        <span>Dials</span>
                      </div>
                      <div className="font-semibold text-purple-600">{metrics.totalDials.toLocaleString()}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <CheckCircle className="w-3 h-3" />
                        <span>Connected</span>
                      </div>
                      <div className="font-semibold text-green-600">{metrics.totalConnected.toLocaleString()}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Clock className="w-3 h-3" />
                        <span>Talk Time</span>
                      </div>
                      <div className="font-semibold text-amber-600">{formatTalkTime(metrics.totalTalkTime)}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Calendar className="w-3 h-3" />
                        <span>Scheduled</span>
                      </div>
                      <div className="font-semibold text-blue-600">{metrics.totalScheduled}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-sm text-gray-500 mb-1">Successful</div>
                      <div className="font-semibold text-teal-600">{metrics.totalSuccessful}</div>
                    </div>
                  </div>

                  {/* FOP Metrics (visible when viewing agent details) */}
                  <div className="grid grid-cols-2 gap-4 text-center border-l border-gray-200 pl-4">
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-sm text-gray-500 mb-1">
                        <Users className="w-3 h-3" />
                        <span>FOP Scheduled</span>
                      </div>
                      <div className="font-semibold text-indigo-600">{metrics.totalFopScheduled}</div>
                    </div>
                    
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="text-sm text-gray-500 mb-1">FOP Successful</div>
                      <div className="font-semibold text-pink-600">{metrics.totalFopSuccessful}</div>
                    </div>
                  </div>

                  {/* Success Rate Badge */}
                  <div className="text-center">
                    <div className="text-sm text-gray-500 mb-1">Success Rate</div>
                    <Badge className={`px-3 py-1 font-semibold ${getSuccessRateColor(metrics.averageSuccessRate)}`}>
                      {metrics.averageSuccessRate.toFixed(1)}%
                    </Badge>
                  </div>

                  {/* View Details Button */}
                  {onAgentSelect && (
                    <Button
                      onClick={() => onAgentSelect(agent.id)}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      size="sm"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  )}
                </div>
              </div>

              {/* Performance Indicators */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Performance indicators</span>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        metrics.averageSuccessRate >= 70 ? 'bg-green-500' : 
                        metrics.averageSuccessRate >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <span>Success Rate</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        metrics.totalDials >= 100 ? 'bg-green-500' : 
                        metrics.totalDials >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <span>Activity</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${
                        (metrics.totalFopScheduled + metrics.totalFopSuccessful) > 0 ? 'bg-blue-500' : 'bg-gray-300'
                      }`}></div>
                      <span>FOP Activity</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default AgentTable;
