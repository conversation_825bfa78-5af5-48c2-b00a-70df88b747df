import React from "react";
import { AnimatedDropdown } from "@/components/ui/animated-dropdown";

interface MonthDropdownProps {
  selectedMonth: string;
  availableMonths: string[];
  onMonthSelect: (month: string) => void;
  isActive: boolean;
}

// Define which months have data (May and June as per specification)
const MONTHS_WITH_DATA = ["May", "Jun"];

export const MonthDropdown: React.FC<MonthDropdownProps> = ({
  selectedMonth,
  availableMonths,
  onMonthSelect,
  isActive,
}) => {
  // Create full year month list with "All Time" option for better UX
  const allMonths = [
    "All Time", "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const monthItems = allMonths.map((month) => ({
    value: month === "All Time" ? "All Time" : month.slice(0, 3), // Convert to short form for backend compatibility
    label: month,
    hasData: month === "All Time" || MONTHS_WITH_DATA.includes(month.slice(0, 3)),
    isSelected: (month === "All Time" && selectedMonth === "All Time") ||
                (month.slice(0, 3) === selectedMonth),
  }));

  const handleMonthSelect = (value: string) => {
    onMonthSelect(value);
  };

  // Display full month name or "All Time"
  const displayText = selectedMonth === "All Time" ? "All Time" :
    allMonths.find(month => month.slice(0, 3) === selectedMonth) || selectedMonth;

  return (
    <AnimatedDropdown
      trigger={displayText}
      items={monthItems}
      onSelect={handleMonthSelect}
      selectedValue={selectedMonth}
      isActive={isActive}
      className="relative"
      dropdownClassName="min-w-[140px] max-h-[300px] overflow-y-auto"
    />
  );
};

export default MonthDropdown;
