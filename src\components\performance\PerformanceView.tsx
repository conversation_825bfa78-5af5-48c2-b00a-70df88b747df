
import React, { useState } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { ArrowLeft, TrendingUp, BarChart3, Clock, Calendar, CheckCircle, Phone, Target } from "lucide-react";
import AgentTable from "./AgentTable";
import ProjectTable from "../dashboard/ProjectTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { formatTime } from "@/lib/formatters";

const PerformanceView = () => {
  const { 
    selectedProject,
    setSelectedProject, 
    getProjectById,
    getAgentById,
    getProjectPerformanceMetrics,
    getAgentPerformanceMetrics,
    getPerformanceByProject,
    getPerformanceByAgent,
    formatTalkTime,
    timeFrame,
    setTimeFrame
  } = useDashboard();

  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  
  const project = selectedProject !== "All" 
    ? getProjectById(selectedProject) 
    : { name: "All Projects", id: "all" };

  const agent = selectedAgent ? getAgentById(selectedAgent) : null;

  const handleBackToProjects = () => {
    setSelectedProject("All");
    setSelectedAgent(null);
  };

  const handleBackToProject = () => {
    setSelectedAgent(null);
  };

  const handleAgentSelect = (agentId: string) => {
    setSelectedAgent(agentId);
  };

  // Get performance data and metrics
  const getPerformanceData = () => {
    if (selectedAgent) {
      return {
        metrics: getAgentPerformanceMetrics(selectedAgent),
        rawData: getPerformanceByAgent(selectedAgent)
      };
    } else if (selectedProject !== "All") {
      return {
        metrics: getProjectPerformanceMetrics(selectedProject),
        rawData: getPerformanceByProject(selectedProject)
      };
    }
    return { metrics: null, rawData: [] };
  };

  const { metrics, rawData } = getPerformanceData();

  // Process chart data
  const chartData = rawData.reduce((acc: any[], item) => {
    const existingEntry = acc.find(entry => entry.date === item.date);
    if (existingEntry) {
      existingEntry.dials += item.dials;
      existingEntry.connected += item.connected;
      existingEntry.talkTime += Math.round(item.talk_time / 60); // Convert to minutes
      existingEntry.scheduled += item.scheduled_meetings;
      existingEntry.successful += item.successful_meetings;
    } else {
      acc.push({
        date: item.date,
        dials: item.dials,
        connected: item.connected,
        talkTime: Math.round(item.talk_time / 60),
        scheduled: item.scheduled_meetings,
        successful: item.successful_meetings,
        successRate: item.scheduled_meetings > 0 ? 
          ((item.successful_meetings / item.scheduled_meetings) * 100).toFixed(1) : 0
      });
    }
    return acc;
  }, []).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const chartConfig = {
    dials: { label: "Dials", color: "hsl(var(--chart-1))" },
    connected: { label: "Connected", color: "hsl(var(--chart-2))" },
    talkTime: { label: "Talk Time (min)", color: "hsl(var(--chart-3))" },
    scheduled: { label: "Scheduled", color: "hsl(var(--chart-4))" },
    successful: { label: "Successful", color: "hsl(var(--chart-5))" },
  };

  const renderKPICards = () => {
    if (!metrics) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Dials</p>
                <p className="text-2xl font-bold text-purple-600">{metrics.totalDials.toLocaleString()}</p>
              </div>
              <Phone className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Connected</p>
                <p className="text-2xl font-bold text-green-600">{metrics.totalConnected.toLocaleString()}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Talk Time</p>
                <p className="text-2xl font-bold text-amber-600">{formatTalkTime(metrics.totalTalkTime)}</p>
              </div>
              <Clock className="h-8 w-8 text-amber-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Scheduled</p>
                <p className="text-2xl font-bold text-blue-600">{metrics.totalScheduled}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Successful</p>
                <p className="text-2xl font-bold text-teal-600">{metrics.totalSuccessful}</p>
              </div>
              <Target className="h-8 w-8 text-teal-500" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderCharts = () => {
    if (!chartData.length) {
      return (
        <div className="text-center py-12">
          <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">No performance data available</p>
          <p className="text-gray-400 text-sm">Add some performance data to see charts</p>
        </div>
      );
    }

    return (
      <div className="space-y-8">
        {/* Time Period Filter */}
        <div className="flex justify-end">
          <div className="flex gap-2">
            {["Daily", "Weekly", "Monthly"].map((filter) => (
              <Button
                key={filter}
                variant={timeFrame === filter ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeFrame(filter as any)}
                className="text-xs transition-all duration-200 hover:scale-105"
              >
                {filter}
              </Button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Talk Time Trend */}
          <Card className="transition-all duration-200 hover:shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg">Talk Time Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line type="monotone" dataKey="talkTime" stroke="var(--color-talkTime)" strokeWidth={2} name="Talk Time (min)" />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Scheduled vs Successful */}
          <Card className="transition-all duration-200 hover:shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg">Scheduled vs Successful Meetings</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="scheduled" fill="var(--color-scheduled)" name="Scheduled" />
                  <Bar dataKey="successful" fill="var(--color-successful)" name="Successful" />
                </BarChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          {selectedAgent && (
            <Button 
              variant="outline" 
              onClick={handleBackToProject}
              className="flex items-center gap-1"
              size="sm"
            >
              <ArrowLeft size={16} />
              Back to Project
            </Button>
          )}
          {selectedProject !== "All" && !selectedAgent && (
            <Button 
              variant="outline" 
              onClick={handleBackToProjects}
              className="flex items-center gap-1"
              size="sm"
            >
              <ArrowLeft size={16} />
              Back to All Projects
            </Button>
          )}
        </div>

        <div className="flex items-center gap-3">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl text-white">
            <TrendingUp className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              {selectedAgent 
                ? `Agent Performance - ${agent?.name}` 
                : selectedProject === "All" 
                  ? "Project Performance Overview" 
                  : `Project Performance - ${project?.name}`
              }
            </h1>
            <p className="text-gray-600 mt-1">
              {selectedAgent 
                ? `Detailed performance metrics and analysis for ${agent?.name}`
                : selectedProject === "All" 
                  ? "Overview of all project performance metrics and analysis" 
                  : `Detailed performance metrics and analysis for ${project?.name}`
              }
            </p>
          </div>
        </div>

        {selectedAgent && (
          <div className="mt-4">
            <Badge className="bg-blue-100 text-blue-800 border-blue-200">
              Project: {project?.name}
            </Badge>
          </div>
        )}
      </div>

      {/* KPI Cards */}
      {(selectedProject !== "All" || selectedAgent) && renderKPICards()}

      {/* Charts Section */}
      {(selectedProject !== "All" || selectedAgent) && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-6">Performance Analytics</h2>
          {renderCharts()}
        </div>
      )}

      {/* Table Section */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">
          {selectedAgent 
            ? "Agent Details" 
            : selectedProject === "All" 
              ? "All Projects" 
              : "Project Agents"
          }
        </h2>
        
        {selectedAgent ? (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Agent Information</h3>
                <p className="text-gray-600">Name: {agent?.name}</p>
                <p className="text-gray-600">Project: {project?.name}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Performance Summary</h3>
                {metrics && (
                  <div className="space-y-1 text-sm">
                    <p className="text-gray-600">Success Rate: <span className="font-semibold">{metrics.averageSuccessRate.toFixed(1)}%</span></p>
                    <p className="text-gray-600">Avg. Talk Time: <span className="font-semibold">{formatTalkTime(Math.round(metrics.totalTalkTime / Math.max(1, metrics.totalConnected)))}</span></p>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : selectedProject === "All" ? (
          <ProjectTable />
        ) : (
          <AgentTable onAgentSelect={handleAgentSelect} />
        )}
      </div>

      {/* Info Messages */}
      {selectedProject !== "All" && !selectedAgent && (
        <div className="py-3 px-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800 text-sm">
          <p className="flex items-center gap-2">
            <span className="font-medium">Note:</span>
            You are currently viewing performance for the {project?.name} project. Click on an agent to see detailed metrics.
          </p>
        </div>
      )}

      {selectedAgent && (
        <div className="py-3 px-4 bg-blue-50 border border-blue-200 rounded-md text-blue-800 text-sm">
          <p className="flex items-center gap-2">
            <span className="font-medium">Note:</span>
            You are currently viewing detailed performance metrics for {agent?.name} in the {project?.name} project.
          </p>
        </div>
      )}
    </div>
  );
};

export default PerformanceView;
