import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

export interface AgentPerformance {
  id: string;
  agent_id: string;
  project_id: string;
  date: string;
  week?: string;
  dials: number;
  connected: number;
  talk_time: number;
  scheduled_meetings: number;
  successful_meetings: number;
  fop_scheduled: number;
  fop_successful: number;
  fop_projects?: string[];
  success_rate: number;
  created_at: string;
  updated_at: string;
}

// Type for inserting new performance data
export interface AgentPerformanceInsert {
  agent_id: string;
  project_id: string;
  date: string;
  week?: string;
  dials: number;
  connected: number;
  talk_time: number;
  scheduled_meetings: number;
  successful_meetings: number;
  fop_scheduled: number;
  fop_successful: number;
  fop_projects?: string[];
}

export const useAgentPerformanceOperations = () => {
  const [performanceData, setPerformanceData] = useState<AgentPerformance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Fetch performance data with retry mechanism
  const fetchPerformanceData = async (retry = false) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Use any type to bypass TypeScript checking for the table name
      const { data, error: fetchError } = await (supabase as any)
        .from('agent_performance')
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) {
        // If table doesn't exist, start with empty data
        if (fetchError.code === '42P01') {
          console.log('Table does not exist, starting with empty data');
          setPerformanceData([]);
          toast({
            title: "Ready for Data Entry",
            description: "No performance data found. You can start entering real data through the Data Entry section.",
            variant: "default"
          });
        } else {
          throw fetchError;
        }
      } else if (data) {
        setPerformanceData(data as AgentPerformance[]);
      }
    } catch (error) {
      console.error('Error fetching performance data:', error);
      setError('Failed to load performance data');
      
      // Start with empty data instead of sample data
      setPerformanceData([]);
      
      if (!retry && retryCount < 2) {
        // Retry after 2 seconds
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchPerformanceData(true);
        }, 2000);
        
        toast({
          title: "Retrying...",
          description: "Attempting to reconnect. Starting with empty data.",
          variant: "default"
        });
      } else {
        toast({
          title: "Ready for Data Entry",
          description: "Unable to connect to performance database. You can start entering real data through the Data Entry section.",
          variant: "default"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch performance data from Supabase
  useEffect(() => {
    fetchPerformanceData();

    // Subscribe to realtime changes for agent_performance (only if table exists)
    const performanceSubscription = supabase
      .channel('agent-performance-changes')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'agent_performance' 
      }, (payload) => {
        console.log('Performance data change:', payload);
        if (payload.eventType === 'INSERT') {
          setPerformanceData((current) => [payload.new as AgentPerformance, ...current]);
        } else if (payload.eventType === 'UPDATE') {
          setPerformanceData((current) => 
            current.map(item => item.id === payload.new.id ? payload.new as AgentPerformance : item)
          );
        } else if (payload.eventType === 'DELETE') {
          setPerformanceData((current) => 
            current.filter(item => item.id !== payload.old.id)
          );
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(performanceSubscription);
    };
  }, []);

  // Convert seconds to hh:mm:ss format
  const formatTalkTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Convert hh:mm:ss format to seconds
  const parseTalkTime = (timeString: string): number => {
    const parts = timeString.split(':');
    if (parts.length !== 3) return 0;
    
    const hours = parseInt(parts[0], 10) || 0;
    const minutes = parseInt(parts[1], 10) || 0;
    const seconds = parseInt(parts[2], 10) || 0;
    
    return (hours * 3600) + (minutes * 60) + seconds;
  };

  // Add new performance data to Supabase
  const addPerformanceData = async (data: AgentPerformanceInsert) => {
    try {
      console.log('Adding performance data to Supabase:', data);
      
      // Calculate success rate
      const success_rate = data.scheduled_meetings > 0 
        ? (data.successful_meetings / data.scheduled_meetings) * 100 
        : 0;

      const dataWithSuccessRate = {
        ...data,
        success_rate
      };

      // Use any type to bypass TypeScript checking
      const { error } = await (supabase as any)
        .from('agent_performance')
        .insert([dataWithSuccessRate]);

      if (error) {
        console.error('Supabase insert error:', error);
        throw error;
      }
      
      toast({
        title: "Success",
        description: "Performance data added successfully",
      });
      return true;
    } catch (error) {
      console.error('Error adding performance data:', error);
      toast({
        title: "Error",
        description: `Failed to add performance data: ${error.message || 'Unknown error'}. Please try again.`,
        variant: "destructive"
      });
      return false;
    }
  };

  // Update existing performance data in Supabase
  const updatePerformanceData = async (id: string, data: Partial<AgentPerformance>) => {
    try {
      console.log('Updating performance data:', { id, data });
      
      // Calculate success rate if needed
      const updateData = { ...data };
      if (updateData.successful_meetings !== undefined && updateData.scheduled_meetings !== undefined) {
        updateData.success_rate = updateData.scheduled_meetings > 0 
          ? (updateData.successful_meetings / updateData.scheduled_meetings) * 100 
          : 0;
      }

      // Use any type to bypass TypeScript checking
      const { error } = await (supabase as any)
        .from('agent_performance')
        .update(updateData)
        .eq('id', id);

      if (error) {
        console.error('Supabase update error:', error);
        throw error;
      }
      
      console.log('Performance data updated successfully');
      return true;
    } catch (error) {
      console.error('Error updating performance data:', error);
      throw new Error(error.message || 'Failed to update performance data');
    }
  };

  // Delete performance data from Supabase
  const deletePerformanceData = async (id: string) => {
    try {
      // Use any type to bypass TypeScript checking
      const { error } = await (supabase as any)
        .from('agent_performance')
        .delete()
        .eq('id', id);

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Performance data deleted successfully",
      });
      return true;
    } catch (error) {
      console.error('Error deleting performance data:', error);
      toast({
        title: "Error",
        description: "Failed to delete performance data. Please try again.",
        variant: "destructive"
      });
      return false;
    }
  };

  // Get performance data by agent
  const getPerformanceByAgent = (agentId: string): AgentPerformance[] => {
    return performanceData.filter(item => item.agent_id === agentId);
  };

  // Get performance data by project
  const getPerformanceByProject = (projectId: string): AgentPerformance[] => {
    return performanceData.filter(item => item.project_id === projectId);
  };

  // Get aggregated metrics for an agent
  const getAgentMetrics = (agentId: string) => {
    const agentData = getPerformanceByAgent(agentId);
    
    return {
      totalDials: agentData.reduce((sum, item) => sum + item.dials, 0),
      totalConnected: agentData.reduce((sum, item) => sum + item.connected, 0),
      totalTalkTime: agentData.reduce((sum, item) => sum + item.talk_time, 0),
      totalScheduled: agentData.reduce((sum, item) => sum + item.scheduled_meetings, 0),
      totalSuccessful: agentData.reduce((sum, item) => sum + item.successful_meetings, 0),
      totalFopScheduled: agentData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0),
      totalFopSuccessful: agentData.reduce((sum, item) => sum + (item.fop_successful || 0), 0),
      averageSuccessRate: agentData.length > 0 ? 
        agentData.reduce((sum, item) => sum + item.success_rate, 0) / agentData.length : 0
    };
  };

  // Get aggregated metrics for a project
  const getProjectMetrics = (projectId: string) => {
    const projectData = getPerformanceByProject(projectId);
    
    return {
      totalDials: projectData.reduce((sum, item) => sum + item.dials, 0),
      totalConnected: projectData.reduce((sum, item) => sum + item.connected, 0),
      totalTalkTime: projectData.reduce((sum, item) => sum + item.talk_time, 0),
      totalScheduled: projectData.reduce((sum, item) => sum + item.scheduled_meetings, 0),
      totalSuccessful: projectData.reduce((sum, item) => sum + item.successful_meetings, 0),
      totalFopScheduled: projectData.reduce((sum, item) => sum + (item.fop_scheduled || 0), 0),
      totalFopSuccessful: projectData.reduce((sum, item) => sum + (item.fop_successful || 0), 0),
      averageSuccessRate: projectData.length > 0 ? 
        projectData.reduce((sum, item) => sum + item.success_rate, 0) / projectData.length : 0
    };
  };

  // Retry function for manual retry
  const retryFetch = () => {
    setRetryCount(0);
    fetchPerformanceData();
  };

  return {
    performanceData,
    isLoading,
    error,
    addPerformanceData,
    updatePerformanceData,
    deletePerformanceData,
    getPerformanceByAgent,
    getPerformanceByProject,
    getAgentMetrics,
    getProjectMetrics,
    formatTalkTime,
    parseTalkTime,
    retryFetch,
  };
};
